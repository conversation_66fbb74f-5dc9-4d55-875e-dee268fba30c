# PostgreSQL Database
apiVersion: v1
kind: Service
metadata:
  name: supabase-postgres
  namespace: lightbox-idc-1
spec:
  ports:
  - port: 5432
    targetPort: 5432
  selector:
    app: supabase-postgres
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-postgres
  namespace: lightbox-idc-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: supabase-postgres
  template:
    metadata:
      labels:
        app: supabase-postgres
    spec:
      containers:
      - name: postgres
        image: supabase/postgres:15.1.0.147
        env:
        - name: POSTGRES_USER
          value: "supabase"
        - name: POSTGRES_PASSWORD
          value: "Az123!ssd124zz"
        - name: POSTGRES_DB
          value: "supabase"
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-data
        emptyDir: {}
---
# Supabase Studio
apiVersion: v1
kind: Service
metadata:
  name: supabase-studio
  namespace: lightbox-idc-1
spec:
  ports:
  - port: 3000
    targetPort: 3000
  selector:
    app: supabase-studio
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-studio
  namespace: lightbox-idc-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: supabase-studio
  template:
    metadata:
      labels:
        app: supabase-studio
    spec:
      containers:
      - name: studio
        image: supabase/studio:20250130-b048539
        env:
        - name: POSTGRES_HOST
          value: "supabase-postgres"
        - name: POSTGRES_PORT
          value: "5432"
        - name: POSTGRES_DB
          value: "supabase"
        - name: POSTGRES_USER
          value: "supabase"
        - name: POSTGRES_PASSWORD
          value: "Az123!ssd124zz"
        - name: DEFAULT_ORGANIZATION_NAME
          value: "Default Organization"
        - name: DEFAULT_PROJECT_NAME
          value: "Default Project"
        ports:
        - containerPort: 3000
---
# Ingress - 子域名方式
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: supabase-subdomain-clean-ingress
  namespace: lightbox-idc-1
  annotations:
    kubernetes.io/ingress.class: ingress-controller-105564
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "*"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  rules:
  - host: supabase.api-docs.woa.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          serviceName: supabase-studio
          servicePort: 3000
