apiVersion: v1
kind: Service
metadata:
  name: supabase-test-serv
  labels:
    app: supabase-test
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  - name: api
    port: 3000
    targetPort: 3000
    protocol: TCP
  - name: db
    port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app: supabase-test
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-test-deployment
  namespace: supabase-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: supabase-test
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: supabase-test
    spec:
      containers:
      - image: supabase/studio:20250130-b048539
        name: supabase-test
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 80
          name: http
        - containerPort: 3000
          name: api
        - containerPort: 5432
          name: db
        resources:
          limits:
            cpu: 2000m
            memory: 4Gi
          requests:
            cpu: 1000m
            memory: 2Gi
        env:
        - name: POSTGRES_PASSWORD
          value: "Az123!ssd124zz"
        - name: POSTGRES_USER
          value: "supabase"
        - name: POSTGRES_DB
          value: "supabase"
        - name: SUPABASE_ANON_KEY
          value: "your-anon-key"
        - name: SUPABASE_SERVICE_KEY
          value: "your-service-key"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: supabase-data
          mountPath: /var/lib/supabase/data
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: supabase-postgres-pvc
      - name: supabase-data
        persistentVolumeClaim:
          claimName: supabase-data-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supabase-postgres-pvc
  namespace: supabase-test
spec:
  storageClassName: nfs-client
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supabase-data-pvc
  namespace: supabase-test
spec:
  storageClassName: nfs-client
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 20Gi
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: supabase-test-ingress
  namespace: supabase-test
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "*"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  rules:
  - host: supabase-test.local
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: supabase-test-serv
            port:
              number: 3000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: supabase-test-serv
            port:
              number: 80
