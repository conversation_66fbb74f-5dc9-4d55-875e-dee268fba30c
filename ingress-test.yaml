apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: supabase-test-ingress
  namespace: lightbox-idc-1
  annotations:
    kubernetes.io/ingress.class: ingress-controller-37898
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "*"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  rules:
  - host: api-docs.woa.com
    http:
      paths:
      - path: /supabase
        pathType: Prefix
        backend:
          serviceName: supabase-studio
          servicePort: 3000
