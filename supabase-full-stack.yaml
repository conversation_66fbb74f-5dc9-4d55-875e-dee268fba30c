# PostgreSQL Database
apiVersion: v1
kind: Service
metadata:
  name: supabase-postgres
  namespace: lightbox-idc-1
spec:
  ports:
  - port: 5432
    targetPort: 5432
  selector:
    app: supabase-postgres
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-postgres
  namespace: lightbox-idc-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: supabase-postgres
  template:
    metadata:
      labels:
        app: supabase-postgres
    spec:
      containers:
      - name: postgres
        image: supabase/postgres:15.1.0.147
        env:
        - name: POSTGRES_USER
          value: "supabase"
        - name: POSTGRES_PASSWORD
          value: "Az123!ssd124zz"
        - name: POSTGRES_DB
          value: "supabase"
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-data
        emptyDir: {}
---
# Kong API Gateway
apiVersion: v1
kind: Service
metadata:
  name: supabase-kong
  namespace: lightbox-idc-1
spec:
  ports:
  - name: http
    port: 8000
    targetPort: 8000
  - name: https
    port: 8443
    targetPort: 8443
  selector:
    app: supabase-kong
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-kong
  namespace: lightbox-idc-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: supabase-kong
  template:
    metadata:
      labels:
        app: supabase-kong
    spec:
      containers:
      - name: kong
        image: kong:2.8.1
        env:
        - name: KONG_DATABASE
          value: "off"
        - name: KONG_DECLARATIVE_CONFIG
          value: "/var/lib/kong/kong.yml"
        - name: KONG_DNS_ORDER
          value: "LAST,A,CNAME"
        - name: KONG_PLUGINS
          value: "request-transformer,cors,key-auth,acl,basic-auth"
        - name: KONG_NGINX_PROXY_PROXY_BUFFER_SIZE
          value: "160k"
        - name: KONG_NGINX_PROXY_PROXY_BUFFERS
          value: "64 160k"
        ports:
        - containerPort: 8000
        - containerPort: 8443
---
# Supabase Studio
apiVersion: v1
kind: Service
metadata:
  name: supabase-studio
  namespace: lightbox-idc-1
spec:
  ports:
  - port: 3000
    targetPort: 3000
  selector:
    app: supabase-studio
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-studio
  namespace: lightbox-idc-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: supabase-studio
  template:
    metadata:
      labels:
        app: supabase-studio
    spec:
      containers:
      - name: studio
        image: supabase/studio:20250130-b048539
        env:
        - name: POSTGRES_HOST
          value: "supabase-postgres"
        - name: POSTGRES_PORT
          value: "5432"
        - name: POSTGRES_DB
          value: "postgres"
        - name: POSTGRES_USER
          value: "postgres"
        - name: POSTGRES_PASSWORD
          value: "your-super-secret-and-long-postgres-password"
        - name: DEFAULT_ORGANIZATION_NAME
          value: "Default Organization"
        - name: DEFAULT_PROJECT_NAME
          value: "Default Project"
        ports:
        - containerPort: 3000
---
# Ingress
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: supabase-full-ingress
  namespace: lightbox-idc-1
  annotations:
    kubernetes.io/ingress.class: ingress-controller-37898
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "*"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  rules:
  - host: api-docs.woa.com
    http:
      paths:
      - path: /supabase(/|$)(.*)
        backend:
          serviceName: supabase-studio
          servicePort: 3000
      - path: /supabase/api(/|$)(.*)
        backend:
          serviceName: supabase-kong
          servicePort: 8000
