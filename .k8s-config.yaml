app_name: supabase_deployment
label_name: supabase-deployment
template: deployment
output_yaml_basename: supabase_deployment
startup_command: ""
components:
  backend:
    name: supabase-backend
    label: supabase-backend
  frontend:
    name: supabase-studio
    label: supabase-studio
docker_image:
  name: supabase_deployment
branches:
  sxz:
    env:
      - env_key: POSTGRES_USER
        env_value: 'supabase'
      - env_key: POSTGRES_HOST
        env_value: '************'
      - env_key: POSTGRES_PORT
        env_value: '5432'
      - env_key: POSTGRES_DB
        env_value: 'supabase'
      - env_key: POSTGRES_PASSWORD
        env_value: 'Az123!ssd124zz'
      - env_key: SUPABASE_ANON_KEY
        env_value: 'your-anon-key'
      - env_key: SUPABASE_SERVICE_KEY
        env_value: 'your-service-key'
      - env_key: STUDIO_PORT
        env_value: '80'
      - env_key: API_PORT
        env_value: '3000'
domain: api-docs.woa.com
ingress:
  hosts:
    - api-docs.woa.com
